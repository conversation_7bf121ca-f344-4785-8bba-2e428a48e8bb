/**
 * Deepseek LLM Provider
 * Implements Deepseek API integration with function calling support
 */

import axios from 'axios';
import { BaseLLMProvider } from './base-provider.js';

export class DeepseekProvider extends BaseLLMProvider {
  #httpClient;
  #apiKey;
  #baseUrl;
  #models;

  constructor(config, logger) {
    super('deepseek', config, logger);

    const providerConfig = this.getProviderConfig();
    // Use environment variable if available, otherwise use config
    this.#apiKey = process.env.DEEPSEEK_API_KEY || providerConfig.apiKey;
    this.#baseUrl = process.env.DEEPSEEK_BASE_URL || providerConfig.baseUrl;
    this.#models = providerConfig.models;

    this.#initializeHttpClient();
  }

  async initialize() {
    this.log('info', 'Initializing Deepseek provider');

    if (!this.#apiKey) {
      this.log('warn', 'No API key provided for Deepseek. Please set DEEPSEEK_API_KEY environment variable or configure apiKey in config file.');
      this.setAvailable(false);
      return;
    }

    if (this.#apiKey.length < 10) {
      this.log('warn', 'Invalid API key format for Deepseek. API key appears to be too short.');
      this.setAvailable(false);
      return;
    }

    try {
      await this.checkAvailability();
      this.setAvailable(true);
      this.log('info', 'Deepseek provider initialized successfully');
    } catch (error) {
      this.log('error', 'Failed to initialize Deepseek provider', {
        error: error.message,
        suggestion: 'Please check your API key and network connection'
      });
      this.setAvailable(false);
    }
  }

  async checkAvailability() {
    try {
      this.log('debug', 'Checking Deepseek API availability');
      const response = await this.#httpClient.get('/models');

      if (response.status === 200) {
        this.log('debug', 'Deepseek API is available');
        return true;
      } else {
        throw new Error(`Unexpected response status: ${response.status}`);
      }
    } catch (error) {
      this.log('debug', 'Deepseek API availability check failed', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        message: error.message
      });

      if (error.response?.status === 401) {
        throw new Error('Invalid API key. Please check your Deepseek API key.');
      }
      if (error.response?.status === 403) {
        throw new Error('Access forbidden. Please check your API key permissions.');
      }
      if (error.response?.status === 429) {
        throw new Error('Rate limit exceeded during availability check.');
      }
      if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
        throw new Error('Cannot connect to Deepseek API. Please check your internet connection.');
      }

      throw new Error(`Deepseek API unavailable: ${error.message}`);
    }
  }

  async generateCompletion(messages, options = {}) {
    this.validateMessages(messages);

    const requestOptions = this.mergeOptions(options);
    const model = options.model || this.#models.chat;

    const requestData = {
      model,
      messages: this.prepareMessages(messages),
      ...requestOptions
    };

    this.log('debug', 'Generating completion', {
      model,
      messageCount: messages.length,
      options: requestOptions
    });

    try {
      const response = await this.#httpClient.post('/chat/completions', requestData);

      const content = this.extractContent(response.data);
      const usage = this.extractUsage(response.data);

      this.log('debug', 'Completion generated successfully', {
        usage,
        contentLength: content.length
      });

      return this.formatSuccess({
        content,
        usage,
        model,
        finishReason: response.data.choices[0].finish_reason
      });

    } catch (error) {
      this.log('error', 'Failed to generate completion', {
        error: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data
      });

      if (error.response?.status === 422) {
        const errorData = error.response.data;
        let errorMessage = 'Invalid request format';

        if (errorData?.error?.message) {
          errorMessage = errorData.error.message;
        } else if (errorData?.message) {
          errorMessage = errorData.message;
        }

        this.log('error', 'API request validation failed', {
          requestData: JSON.stringify(requestData, null, 2),
          errorData
        });

        throw new Error(`Deepseek API validation error: ${errorMessage}`);
      }

      if (error.response?.status === 401) {
        throw new Error('Invalid API key. Please check your Deepseek API key configuration.');
      }

      if (error.response?.status === 429) {
        await this.handleRateLimit(error, error.response.headers['retry-after']);
        throw new Error('Rate limit exceeded');
      }

      if (error.response?.status === 403) {
        throw new Error('Access forbidden. Please check your API key permissions.');
      }

      if (error.response?.status >= 500) {
        throw new Error(`Deepseek API server error (${error.response.status}): ${error.response.statusText}`);
      }

      throw error;
    }
  }

  async generateCompletionWithTools(messages, tools, options = {}) {
    this.validateMessages(messages);
    this.validateTools(tools);
    
    const requestOptions = this.mergeOptions(options);
    const model = options.model || this.#models.chat;
    
    const requestData = {
      model,
      messages: this.prepareMessages(messages),
      tools,
      tool_choice: options.tool_choice || 'auto',
      ...requestOptions
    };

    this.log('debug', 'Generating completion with tools', { 
      model, 
      messageCount: messages.length,
      toolCount: tools.length,
      options: requestOptions 
    });

    try {
      const response = await this.#httpClient.post('/chat/completions', requestData);
      
      const choice = response.data.choices[0];
      const message = choice.message;
      const usage = this.extractUsage(response.data);
      
      let result = {
        usage,
        model,
        finishReason: choice.finish_reason
      };

      // Check if there are tool calls
      if (message.tool_calls && message.tool_calls.length > 0) {
        result.toolCalls = message.tool_calls.map(call => ({
          id: call.id,
          type: call.type,
          function: {
            name: call.function.name,
            arguments: JSON.parse(call.function.arguments)
          }
        }));
      } else {
        result.content = message.content || '';
      }
      
      this.log('debug', 'Completion with tools generated successfully', { 
        usage,
        hasToolCalls: !!result.toolCalls,
        toolCallCount: result.toolCalls?.length || 0
      });

      return this.formatSuccess(result);

    } catch (error) {
      this.log('error', 'Failed to generate completion with tools', {
        error: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data
      });

      if (error.response?.status === 422) {
        const errorData = error.response.data;
        let errorMessage = 'Invalid request format for tools completion';

        if (errorData?.error?.message) {
          errorMessage = errorData.error.message;
        } else if (errorData?.message) {
          errorMessage = errorData.message;
        }

        this.log('error', 'API request validation failed for tools', {
          requestData: JSON.stringify({ model, messages: this.prepareMessages(messages), tools }, null, 2),
          errorData
        });

        throw new Error(`Deepseek API validation error (tools): ${errorMessage}`);
      }

      if (error.response?.status === 401) {
        throw new Error('Invalid API key for tools completion. Please check your Deepseek API key configuration.');
      }

      if (error.response?.status === 429) {
        await this.handleRateLimit(error, error.response.headers['retry-after']);
        throw new Error('Rate limit exceeded during tools completion');
      }

      if (error.response?.status === 403) {
        throw new Error('Access forbidden for tools completion. Please check your API key permissions.');
      }

      if (error.response?.status >= 500) {
        throw new Error(`Deepseek API server error during tools completion (${error.response.status}): ${error.response.statusText}`);
      }

      throw error;
    }
  }

  async streamCompletion(messages, options = {}) {
    this.validateMessages(messages);
    
    const requestOptions = this.mergeOptions(options);
    const model = options.model || this.#models.chat;
    
    const requestData = {
      model,
      messages: this.prepareMessages(messages),
      stream: true,
      ...requestOptions
    };

    this.log('debug', 'Starting streaming completion', { 
      model, 
      messageCount: messages.length 
    });

    try {
      const response = await this.#httpClient.post('/chat/completions', requestData, {
        responseType: 'stream'
      });

      return this.#createStreamIterator(response.data);

    } catch (error) {
      this.log('error', 'Failed to start streaming completion', { error: error.message });
      throw error;
    }
  }

  async getAvailableModels() {
    try {
      const response = await this.#httpClient.get('/models');
      
      const models = response.data.data.map(model => ({
        id: model.id,
        name: model.id,
        description: model.description || '',
        contextLength: model.context_length || 4096
      }));

      this.log('debug', 'Retrieved available models', { count: models.length });
      
      return models;

    } catch (error) {
      this.log('error', 'Failed to get available models', { error: error.message });
      throw error;
    }
  }

  // Private methods

  #initializeHttpClient() {
    const providerConfig = this.getProviderConfig();
    
    this.#httpClient = axios.create({
      baseURL: this.#baseUrl,
      timeout: providerConfig.timeout,
      headers: {
        'Authorization': `Bearer ${this.#apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    // Request interceptor
    this.#httpClient.interceptors.request.use(
      (config) => {
        this.log('debug', 'Making API request', {
          method: config.method?.toUpperCase(),
          url: config.url,
          dataSize: config.data ? JSON.stringify(config.data).length : 0
        });
        return config;
      },
      (error) => {
        this.log('error', 'Request interceptor error', { error: error.message });
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.#httpClient.interceptors.response.use(
      (response) => {
        this.log('debug', 'API request completed', {
          status: response.status,
          url: response.config.url,
          responseSize: JSON.stringify(response.data).length
        });
        return response;
      },
      (error) => {
        this.log('error', 'API request failed', {
          status: error.response?.status,
          url: error.config?.url,
          error: error.message
        });
        return Promise.reject(error);
      }
    );
  }

  async *#createStreamIterator(stream) {
    let buffer = '';
    
    for await (const chunk of stream) {
      buffer += chunk.toString();
      
      const lines = buffer.split('\n');
      buffer = lines.pop() || ''; // Keep incomplete line in buffer
      
      for (const line of lines) {
        if (line.trim() === '') continue;
        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          
          if (data === '[DONE]') {
            return;
          }
          
          try {
            const parsed = JSON.parse(data);
            const choice = parsed.choices?.[0];
            
            if (choice?.delta?.content) {
              yield {
                content: choice.delta.content,
                finishReason: choice.finish_reason,
                usage: parsed.usage
              };
            }
          } catch (error) {
            this.log('warn', 'Failed to parse streaming chunk', { error: error.message });
          }
        }
      }
    }
  }

  /**
   * Override prepareMessages to handle function messages for Deepseek
   */
  prepareMessages(messages) {
    return messages.map(message => {
      // Convert function messages to user messages for Deepseek compatibility
      if (message.role === 'function') {
        return {
          role: 'user',
          content: `Tool "${message.name}" result:\n${message.content}`
        };
      }

      // Ensure content is always a string
      let content = message.content;
      if (typeof content !== 'string') {
        if (content === null || content === undefined) {
          content = '';
        } else {
          content = JSON.stringify(content);
        }
      }

      // Create the base message
      const preparedMessage = {
        role: message.role,
        content: content
      };

      // Only add optional fields if they exist and are valid
      if (message.name && typeof message.name === 'string') {
        preparedMessage.name = message.name;
      }

      if (message.function_call && typeof message.function_call === 'object') {
        preparedMessage.function_call = message.function_call;
      }

      if (message.tool_calls && Array.isArray(message.tool_calls)) {
        preparedMessage.tool_calls = message.tool_calls;
      }

      return preparedMessage;
    });
  }

  getDefaultOptions() {
    const baseOptions = super.getDefaultOptions();
    return {
      ...baseOptions,
      // Deepseek-specific defaults
      stream: false,
      stop: null
    };
  }
}
