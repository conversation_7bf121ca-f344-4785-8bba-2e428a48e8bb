# 🤖 Arien-AI

A modern, powerful CLI terminal system with LLM function tools calling capabilities, shell command execution, and intelligent agent behavior with never-give-up retry logic.

## ✨ Features

### 🧠 AI-Powered Intelligence
- **Multiple LLM Providers**: Deepseek (deepseek-chat, deepseek-reasoner) and Ollama support
- **Function Calling**: Advanced tool orchestration with parallel and sequential execution
- **Context Awareness**: Maintains conversation history and learns from interactions
- **Intelligent Planning**: Breaks down complex tasks into actionable steps

### 🛠️ Powerful Tools
- **Shell Tool**: Execute system commands safely with comprehensive monitoring
- **Web Tool**: HTTP requests, web scraping, and API interactions
- **Cross-Platform**: Windows 11 WSL, macOS, and Linux support
- **Safety First**: Command validation, permission checks, and user confirmations

### 🔄 Never Give Up Logic
- **Exponential Backoff**: Smart retry strategies for temporary failures
- **Circuit Breaker**: Prevents cascading failures
- **Error Recovery**: Automatic analysis and alternative suggestions
- **Graceful Degradation**: Continues operation even when some components fail

### 🎨 Modern CLI Experience
- **Interactive Mode**: Natural language conversations with the AI
- **Real-time Progress**: Custom ball animation with elapsed time
- **Rich Output**: Syntax highlighting, tables, and formatted displays
- **Command History**: Track and replay previous interactions

## 🚀 Quick Start

### Installation

#### Global Installation (Recommended)
```bash
# Clone the repository
git clone https://github.com/arien-ai/arien-ai.git
cd arien-ai

# Install globally
node scripts/install.js --install

# Initialize configuration
arien config --init
```

#### Manual Installation
```bash
# Install dependencies
npm install

# Start interactive mode
npm start
```

### First Run

1. **Initialize Configuration**
   ```bash
   arien config --init
   ```

2. **Start Interactive Mode**
   ```bash
   arien interactive
   ```

3. **Try Some Commands**
   ```
   arien> list files in current directory
   arien> check system memory usage
   arien> download https://example.com/file.txt
   ```

## 📖 Usage

### Interactive Mode
```bash
arien interactive
# or simply
arien
```

### Single Command Execution
```bash
arien execute "list all running processes"
arien exec "check disk space and memory usage"
```

### System Status
```bash
arien status
```

### Configuration Management
```bash
arien config --init     # Initialize configuration
arien config --reset    # Reset to defaults
arien config            # Show current configuration
```

## 🔧 Configuration

Arien-AI uses a JSON configuration file located at `~/.arien-ai/config.json`. Key settings include:

### LLM Providers
```json
{
  "llm": {
    "providers": {
      "deepseek": {
        "enabled": true,
        "apiKey": "your-api-key",
        "models": {
          "chat": "deepseek-chat",
          "reasoner": "deepseek-reasoner"
        }
      },
      "ollama": {
        "enabled": true,
        "baseUrl": "http://localhost:11434"
      }
    },
    "defaultProvider": "deepseek",
    "fallbackProvider": "ollama"
  }
}
```

### Tool Configuration
```json
{
  "tools": {
    "shell": {
      "enabled": true,
      "timeout": 30000,
      "requireConfirmation": ["rm", "del", "format", "sudo"]
    },
    "web": {
      "enabled": true,
      "timeout": 15000,
      "maxResponseSize": 5242880
    }
  }
}
```

## 🛠️ Available Tools

### Shell Tool
Execute system commands with safety checks and monitoring.

**Examples:**
- `"list files in the current directory"`
- `"check system memory and CPU usage"`
- `"find all Python files in the project"`
- `"create a new directory called 'my-project'"`

**Safety Features:**
- Command validation and sanitization
- Blocked dangerous commands
- User confirmation for destructive operations
- Timeout management
- Cross-platform compatibility

### Web Tool
Make HTTP requests and scrape web content.

**Examples:**
- `"download the latest Node.js documentation"`
- `"check if google.com is accessible"`
- `"get JSON data from https://api.github.com/users/octocat"`
- `"scrape the title and headings from https://example.com"`

**Features:**
- Multiple HTTP methods (GET, POST, PUT, DELETE, PATCH)
- Web scraping with CSS selectors
- File downloads
- Response parsing (JSON, HTML, text)
- Cookie and session management

## 🔄 Workflows

Arien-AI supports predefined workflows for common tasks:

### System Information
```bash
arien execute "run system information workflow"
```

### Network Diagnostics
```bash
arien execute "perform network diagnostics"
```

### Project Setup
```bash
arien execute "create a new Node.js project called 'my-app'"
```

## 🎯 Examples

### File Operations
```
arien> list all JavaScript files in the src directory
arien> create a backup of the config.json file
arien> find files larger than 100MB
```

### System Monitoring
```
arien> show me the top 10 processes by memory usage
arien> check disk space on all mounted drives
arien> monitor network connections
```

### Development Tasks
```
arien> initialize a new Git repository
arien> install npm dependencies and run tests
arien> build the project and check for errors
```

### Web Operations
```
arien> download the latest release from GitHub
arien> check the status of my website
arien> scrape product information from an e-commerce site
```

## 🔒 Security

Arien-AI prioritizes security and safety:

- **Command Validation**: All commands are validated before execution
- **Blocked Commands**: Dangerous operations are blocked by default
- **User Confirmation**: Destructive actions require explicit confirmation
- **Sandboxing**: Optional sandbox mode for additional isolation
- **Audit Logging**: All operations are logged for review

## 🐛 Troubleshooting

### Quick Diagnostics
Run the built-in diagnostic tool to check your configuration:
```bash
npm run diagnose
```

### Common Issues

1. **"All providers failed" or HTTP 422 errors**
   - **Missing API Key**: Set your Deepseek API key
     ```bash
     # Option 1: Environment variable
     export DEEPSEEK_API_KEY="your_api_key_here"

     # Option 2: Create .env file
     cp .env.example .env
     # Edit .env and add your API key
     ```
   - **Invalid API Key**: Verify your key at https://platform.deepseek.com/
   - **API Key Format**: Ensure the key is complete and not truncated

2. **"Provider not available: ollama"**
   - Install Ollama: https://ollama.ai/
   - Start the service: `ollama serve`
   - Pull a model: `ollama pull llama3.2:latest`
   - Check connection: `curl http://localhost:11434/api/tags`

3. **"Request failed with status code 422"**
   - This indicates invalid request format to the API
   - Check your API key is valid and has proper permissions
   - Verify the model name in configuration
   - Run diagnostics: `npm run diagnose`

4. **"Command not found"**
   - Ensure the global installation completed successfully
   - Check that the bin directory is in your PATH
   - Try running with the full path

5. **"Permission denied"**
   - Check file/directory permissions
   - Run with appropriate privileges if needed
   - Verify the command is not blocked in configuration

### Environment Variables
Create a `.env` file in the project root:
```bash
# Copy the example file
cp .env.example .env

# Edit with your values
DEEPSEEK_API_KEY=your_deepseek_api_key_here
OLLAMA_BASE_URL=http://localhost:11434
LLM_DEFAULT_PROVIDER=deepseek
LLM_FALLBACK_PROVIDER=ollama
```

### Debug Mode
```bash
arien --verbose execute "your command"
```

### Logs
Check the log file at `~/.arien-ai/logs/arien-ai.log` for detailed information.

### Getting Help
1. Run diagnostics: `npm run diagnose`
2. Check the logs for detailed error messages
3. Verify your API keys and network connectivity
4. Ensure all required services are running

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Setup
```bash
git clone https://github.com/arien-ai/arien-ai.git
cd arien-ai
npm install
npm run dev
```

### Running Tests
```bash
npm test
npm run test:unit
npm run test:integration
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built with modern ES2025 JavaScript features
- Powered by Deepseek and Ollama LLM providers
- Inspired by the need for intelligent CLI automation

## 📞 Support

- 📧 Email: <EMAIL>
- 💬 Discord: [Join our community](https://discord.gg/arien-ai)
- 🐛 Issues: [GitHub Issues](https://github.com/arien-ai/arien-ai/issues)
- 📖 Documentation: [Full Documentation](https://docs.arien-ai.com)

---

**Made with ❤️ by the Arien-AI Team**
