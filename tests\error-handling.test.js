/**
 * Error Handling Tests
 * Tests for improved error handling and provider fallback logic
 */

import { test, describe } from 'node:test';
import assert from 'node:assert';
import { DeepseekProvider } from '../src/llm/providers/deepseek-provider.js';
import { OllamaProvider } from '../src/llm/providers/ollama-provider.js';
import { LLMClient } from '../src/llm/llm-client.js';
import { Config } from '../src/utils/config.js';

// Mock logger
const mockLogger = {
  info: () => {},
  warn: () => {},
  error: () => {},
  debug: () => {}
};

// Mock config
const mockConfig = {
  get: (path) => {
    if (path === 'llm') {
      return {
        providers: {
          deepseek: {
            enabled: true,
            apiKey: '',
            baseUrl: 'https://api.deepseek.com/v1',
            models: { chat: 'deepseek-chat' },
            timeout: 30000
          },
          ollama: {
            enabled: true,
            baseUrl: 'http://localhost:11434',
            models: { default: 'llama3.2:latest' },
            timeout: 60000
          }
        },
        defaultProvider: 'deepseek',
        fallbackProvider: 'ollama'
      };
    }
    return {};
  },
  getLLMConfig: (provider) => {
    const llmConfig = mockConfig.get('llm');
    return llmConfig.providers[provider || llmConfig.defaultProvider];
  },
  isProviderEnabled: (provider) => true
};

describe('Error Handling Tests', () => {
  
  test('Deepseek provider should handle missing API key gracefully', async () => {
    const provider = new DeepseekProvider(mockConfig, mockLogger);
    
    await provider.initialize();
    
    // Provider should be unavailable when no API key is provided
    assert.strictEqual(provider.isAvailable(), false);
  });

  test('Deepseek provider should handle invalid API key format', async () => {
    const configWithShortKey = {
      ...mockConfig,
      get: (path) => {
        if (path === 'llm') {
          const config = mockConfig.get('llm');
          config.providers.deepseek.apiKey = 'short';
          return config;
        }
        return {};
      }
    };

    const provider = new DeepseekProvider(configWithShortKey, mockLogger);
    
    await provider.initialize();
    
    // Provider should be unavailable when API key is too short
    assert.strictEqual(provider.isAvailable(), false);
  });

  test('Ollama provider should handle connection refused gracefully', async () => {
    const provider = new OllamaProvider(mockConfig, mockLogger);
    
    await provider.initialize();
    
    // Provider should be unavailable when Ollama is not running
    // (This will fail in CI/test environment where Ollama is not running)
    assert.strictEqual(provider.isAvailable(), false);
  });

  test('LLM client should validate configuration on initialization', async () => {
    const client = new LLMClient(mockConfig, mockLogger);
    
    // This should not throw an error even with invalid configuration
    await assert.doesNotReject(async () => {
      await client.initialize();
    });
  });

  test('LLM client should provide helpful error messages when all providers fail', async () => {
    const client = new LLMClient(mockConfig, mockLogger);
    await client.initialize();
    
    const testMessages = [
      { role: 'user', content: 'Hello, test message' }
    ];

    // This should throw a descriptive error about provider availability
    await assert.rejects(
      async () => {
        await client.generateCompletion(testMessages);
      },
      (error) => {
        return error.message.includes('No available providers') || 
               error.message.includes('not available');
      }
    );
  });

  test('Environment variables should override config values', () => {
    // Set test environment variable
    process.env.DEEPSEEK_API_KEY = 'test_key_from_env';
    
    const provider = new DeepseekProvider(mockConfig, mockLogger);
    
    // The provider should use the environment variable
    // Note: This tests the constructor logic, not the full initialization
    assert.ok(provider);
    
    // Clean up
    delete process.env.DEEPSEEK_API_KEY;
  });

  test('Config should apply environment variables correctly', async () => {
    // Set test environment variables
    process.env.DEEPSEEK_API_KEY = 'test_key';
    process.env.LLM_DEFAULT_PROVIDER = 'ollama';
    
    const config = new Config();
    await config.load();
    
    const llmConfig = config.get('llm');
    
    // Environment variables should override config values
    assert.strictEqual(llmConfig.providers.deepseek.apiKey, 'test_key');
    assert.strictEqual(llmConfig.defaultProvider, 'ollama');
    
    // Clean up
    delete process.env.DEEPSEEK_API_KEY;
    delete process.env.LLM_DEFAULT_PROVIDER;
  });

});

// Run a simple integration test if this file is run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  console.log('Running error handling tests...');
  
  // Test basic provider initialization
  const testBasicProviders = async () => {
    console.log('Testing provider initialization...');
    
    const deepseek = new DeepseekProvider(mockConfig, mockLogger);
    await deepseek.initialize();
    console.log(`Deepseek available: ${deepseek.isAvailable()}`);
    
    const ollama = new OllamaProvider(mockConfig, mockLogger);
    await ollama.initialize();
    console.log(`Ollama available: ${ollama.isAvailable()}`);
    
    const client = new LLMClient(mockConfig, mockLogger);
    await client.initialize();
    console.log(`Available providers: ${client.getAvailableProviders().join(', ')}`);
    
    console.log('Provider tests completed.');
  };
  
  testBasicProviders().catch(console.error);
}
