{"name": "arien-ai", "version": "1.0.0", "description": "A modern CLI terminal system with LLM function tools calling capabilities", "main": "src/index.js", "type": "module", "bin": {"arien": "./src/index.js"}, "scripts": {"start": "node src/index.js", "dev": "node --watch src/index.js", "test": "node --test tests/**/*.test.js", "test:unit": "node --test tests/unit/**/*.test.js", "test:integration": "node --test tests/integration/**/*.test.js", "test:e2e": "node --test tests/e2e/**/*.test.js", "install:global": "node scripts/install.js", "diagnose": "node scripts/diagnose.js", "lint": "eslint src/**/*.js", "format": "prettier --write src/**/*.js"}, "keywords": ["cli", "terminal", "ai", "llm", "agent", "automation", "shell", "tools"], "author": "Arien-AI Team", "license": "MIT", "engines": {"node": ">=20.0.0"}, "dependencies": {"chalk": "^5.3.0", "commander": "^12.0.0", "inquirer": "^9.2.15", "ora": "^8.0.1", "axios": "^1.6.7", "ws": "^8.16.0", "node-fetch": "^3.3.2", "cheerio": "^1.0.0-rc.12", "dotenv": "^16.4.1", "yaml": "^2.3.4", "zod": "^3.22.4", "nanoid": "^5.0.4", "p-retry": "^6.2.0", "p-timeout": "^6.1.2", "p-queue": "^8.0.1", "execa": "^8.0.1", "cross-spawn": "^7.0.3", "which": "^4.0.0", "os-name": "^5.1.0", "update-notifier": "^7.0.0"}, "devDependencies": {"eslint": "^8.56.0", "prettier": "^3.2.4", "nodemon": "^3.0.3"}, "repository": {"type": "git", "url": "https://github.com/arien-ai/arien-ai.git"}, "bugs": {"url": "https://github.com/arien-ai/arien-ai/issues"}, "homepage": "https://github.com/arien-ai/arien-ai#readme", "preferGlobal": true, "os": ["darwin", "linux", "win32"]}