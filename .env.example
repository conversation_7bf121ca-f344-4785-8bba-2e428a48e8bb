# Arien-AI Environment Configuration
# Copy this file to .env and fill in your values

# ===== LLM Provider Configuration =====

# Deepseek API Configuration
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com/v1
DEEPSEEK_ENABLED=true

# Ollama Configuration
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_ENABLED=true

# ===== Provider Selection =====

# Primary LLM provider (deepseek or ollama)
LLM_DEFAULT_PROVIDER=deepseek

# Fallback LLM provider (deepseek or ollama)
LLM_FALLBACK_PROVIDER=ollama

# ===== Logging Configuration =====

# Log level: error, warn, info, debug
LOG_LEVEL=info

# ===== Security Configuration =====

# Require confirmation for destructive actions (true/false)
CONFIRM_DESTRUCTIVE_ACTIONS=true

# ===== Usage Instructions =====

# 1. Copy this file to .env:
#    cp .env.example .env
#
# 2. Get your Deepseek API key:
#    - Visit https://platform.deepseek.com/
#    - Sign up or log in
#    - Go to API Keys section
#    - Create a new API key
#    - Copy the key and paste it as DEEPSEEK_API_KEY value
#
# 3. Install and start Ollama (optional):
#    - Visit https://ollama.ai/
#    - Download and install Ollama
#    - Run: ollama serve
#    - Pull a model: ollama pull llama3.2:latest
#
# 4. Start Arien-AI:
#    npm start
#
# Note: Environment variables override config file settings
