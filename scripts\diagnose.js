#!/usr/bin/env node

/**
 * Arien-AI Diagnostic Script
 * Helps troubleshoot configuration and provider issues
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import chalk from 'chalk';
import axios from 'axios';
import { existsSync } from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

class Diagnostics {
  constructor() {
    this.results = [];
  }

  log(level, message, details = null) {
    const timestamp = new Date().toISOString();
    const colors = {
      info: chalk.blue,
      success: chalk.green,
      warning: chalk.yellow,
      error: chalk.red
    };
    
    console.log(`${colors[level](`[${level.toUpperCase()}]`)} ${message}`);
    if (details) {
      console.log(chalk.gray(JSON.stringify(details, null, 2)));
    }
    
    this.results.push({ timestamp, level, message, details });
  }

  async checkEnvironment() {
    this.log('info', '🔍 Checking Environment...');
    
    // Node.js version
    const nodeVersion = process.version;
    if (nodeVersion >= 'v20.0.0') {
      this.log('success', `Node.js version: ${nodeVersion} ✅`);
    } else {
      this.log('error', `Node.js version: ${nodeVersion} ❌ (requires >= v20.0.0)`);
    }

    // Environment variables
    const envVars = [
      'DEEPSEEK_API_KEY',
      'DEEPSEEK_BASE_URL',
      'OLLAMA_BASE_URL',
      'LLM_DEFAULT_PROVIDER',
      'LLM_FALLBACK_PROVIDER'
    ];

    this.log('info', 'Environment Variables:');
    for (const envVar of envVars) {
      const value = process.env[envVar];
      if (value) {
        const maskedValue = envVar.includes('KEY') ? '***masked***' : value;
        this.log('success', `  ${envVar}: ${maskedValue} ✅`);
      } else {
        this.log('warning', `  ${envVar}: not set ⚠️`);
      }
    }

    // Config files
    const configPath = join(process.cwd(), 'config', 'default.json');
    const envPath = join(process.cwd(), '.env');
    
    if (existsSync(configPath)) {
      this.log('success', `Config file exists: ${configPath} ✅`);
    } else {
      this.log('error', `Config file missing: ${configPath} ❌`);
    }

    if (existsSync(envPath)) {
      this.log('success', `.env file exists: ${envPath} ✅`);
    } else {
      this.log('warning', `.env file not found: ${envPath} ⚠️`);
      this.log('info', 'Consider creating .env file from .env.example');
    }
  }

  async checkDeepseekAPI() {
    this.log('info', '🤖 Checking Deepseek API...');
    
    const apiKey = process.env.DEEPSEEK_API_KEY;
    const baseUrl = process.env.DEEPSEEK_BASE_URL || 'https://api.deepseek.com/v1';

    if (!apiKey) {
      this.log('error', 'Deepseek API key not found ❌');
      this.log('info', 'Set DEEPSEEK_API_KEY environment variable or configure in config file');
      return;
    }

    if (apiKey.length < 10) {
      this.log('error', 'Deepseek API key appears to be invalid (too short) ❌');
      return;
    }

    try {
      const client = axios.create({
        baseURL: baseUrl,
        timeout: 10000,
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      this.log('info', `Testing connection to ${baseUrl}...`);
      const response = await client.get('/models');
      
      if (response.status === 200) {
        this.log('success', 'Deepseek API connection successful ✅');
        this.log('info', `Available models: ${response.data.data?.length || 0}`);
      } else {
        this.log('error', `Unexpected response status: ${response.status} ❌`);
      }

    } catch (error) {
      if (error.response?.status === 401) {
        this.log('error', 'Deepseek API authentication failed - Invalid API key ❌');
      } else if (error.response?.status === 403) {
        this.log('error', 'Deepseek API access forbidden - Check API key permissions ❌');
      } else if (error.response?.status === 429) {
        this.log('warning', 'Deepseek API rate limit exceeded ⚠️');
      } else if (error.code === 'ENOTFOUND') {
        this.log('error', 'Cannot connect to Deepseek API - Check internet connection ❌');
      } else {
        this.log('error', `Deepseek API error: ${error.message} ❌`);
      }
    }
  }

  async checkOllamaAPI() {
    this.log('info', '🦙 Checking Ollama API...');
    
    const baseUrl = process.env.OLLAMA_BASE_URL || 'http://localhost:11434';

    try {
      const client = axios.create({
        baseURL: baseUrl,
        timeout: 5000,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      this.log('info', `Testing connection to ${baseUrl}...`);
      const response = await client.get('/api/tags');
      
      if (response.status === 200) {
        this.log('success', 'Ollama API connection successful ✅');
        const models = response.data.models || [];
        this.log('info', `Available models: ${models.length}`);
        
        if (models.length === 0) {
          this.log('warning', 'No models found in Ollama ⚠️');
          this.log('info', 'Pull a model with: ollama pull llama3.2:latest');
        } else {
          models.forEach(model => {
            this.log('info', `  - ${model.name} (${model.size})`);
          });
        }
      } else {
        this.log('error', `Unexpected response status: ${response.status} ❌`);
      }

    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        this.log('error', 'Ollama service is not running ❌');
        this.log('info', 'Start Ollama with: ollama serve');
      } else if (error.code === 'ENOTFOUND') {
        this.log('error', 'Cannot connect to Ollama - Check if installed and baseUrl is correct ❌');
      } else {
        this.log('error', `Ollama API error: ${error.message} ❌`);
      }
    }
  }

  async testBasicCompletion() {
    this.log('info', '💬 Testing basic completion...');
    
    // This would require importing the actual LLM client
    // For now, just provide guidance
    this.log('info', 'To test completions, run: npm start -- execute "Hello, test message"');
  }

  printSummary() {
    console.log('\n' + chalk.blue.bold('📋 Diagnostic Summary'));
    console.log('='.repeat(50));
    
    const errorCount = this.results.filter(r => r.level === 'error').length;
    const warningCount = this.results.filter(r => r.level === 'warning').length;
    
    if (errorCount === 0 && warningCount === 0) {
      console.log(chalk.green('✅ All checks passed! Your configuration looks good.'));
    } else {
      if (errorCount > 0) {
        console.log(chalk.red(`❌ ${errorCount} error(s) found`));
      }
      if (warningCount > 0) {
        console.log(chalk.yellow(`⚠️  ${warningCount} warning(s) found`));
      }
    }

    console.log('\n' + chalk.blue.bold('🔧 Quick Fixes:'));
    console.log('1. Copy .env.example to .env and configure your API keys');
    console.log('2. Get Deepseek API key from: https://platform.deepseek.com/');
    console.log('3. Install Ollama from: https://ollama.ai/');
    console.log('4. Start Ollama with: ollama serve');
    console.log('5. Pull a model with: ollama pull llama3.2:latest');
  }

  async run() {
    console.log(chalk.cyan.bold('🔍 Arien-AI Diagnostics\n'));
    
    await this.checkEnvironment();
    console.log('');
    
    await this.checkDeepseekAPI();
    console.log('');
    
    await this.checkOllamaAPI();
    console.log('');
    
    await this.testBasicCompletion();
    
    this.printSummary();
  }
}

// Run diagnostics
const diagnostics = new Diagnostics();
diagnostics.run().catch(error => {
  console.error(chalk.red('💥 Diagnostic script failed:'), error);
  process.exit(1);
});
