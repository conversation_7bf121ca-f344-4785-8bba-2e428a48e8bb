/**
 * Base LLM Provider
 * Abstract base class for all LLM providers
 */

export class BaseLL<PERSON>rovider {
  #config;
  #logger;
  #name;
  #isAvailable = false;

  constructor(name, config, logger) {
    this.#name = name;
    this.#config = config;
    this.#logger = logger;
  }

  /**
   * Initialize the provider
   * Must be implemented by subclasses
   */
  async initialize() {
    throw new Error('initialize() must be implemented by subclass');
  }

  /**
   * Check if provider is available
   * Must be implemented by subclasses
   */
  async checkAvailability() {
    throw new Error('checkAvailability() must be implemented by subclass');
  }

  /**
   * Generate completion
   * Must be implemented by subclasses
   */
  async generateCompletion(messages, options = {}) {
    throw new Error('generateCompletion() must be implemented by subclass');
  }

  /**
   * Generate completion with function calling
   * Must be implemented by subclasses
   */
  async generateCompletionWithTools(messages, tools, options = {}) {
    throw new Error('generateCompletionWithTools() must be implemented by subclass');
  }

  /**
   * Stream completion
   * Optional - can be implemented by subclasses
   */
  async streamCompletion(messages, options = {}) {
    // Default implementation falls back to non-streaming
    const response = await this.generateCompletion(messages, options);
    return {
      async *[Symbol.asyncIterator]() {
        yield response;
      }
    };
  }

  /**
   * Get available models
   * Must be implemented by subclasses
   */
  async getAvailableModels() {
    throw new Error('getAvailableModels() must be implemented by subclass');
  }

  /**
   * Get provider status
   */
  getStatus() {
    return {
      name: this.#name,
      available: this.#isAvailable,
      config: this.getProviderConfig()
    };
  }

  /**
   * Get provider configuration
   */
  getProviderConfig() {
    return this.#config.getLLMConfig(this.#name);
  }

  /**
   * Get provider name
   */
  getName() {
    return this.#name;
  }

  /**
   * Check if provider is available
   */
  isAvailable() {
    return this.#isAvailable;
  }

  /**
   * Set availability status
   */
  setAvailable(available) {
    this.#isAvailable = available;
  }

  /**
   * Log provider activity
   */
  log(level, message, meta = {}) {
    this.#logger[level](`[${this.#name}] ${message}`, {
      provider: this.#name,
      ...meta
    });
  }

  /**
   * Validate messages format
   */
  validateMessages(messages) {
    if (!Array.isArray(messages)) {
      throw new Error('Messages must be an array');
    }

    for (const message of messages) {
      if (!message.role) {
        throw new Error('Each message must have a role');
      }

      if (!['system', 'user', 'assistant', 'function'].includes(message.role)) {
        throw new Error(`Invalid message role: ${message.role}`);
      }

      // Content is required for all roles except function messages can have empty content
      if (!message.content && message.role !== 'function') {
        throw new Error(`Message with role '${message.role}' must have content`);
      }

      // Function messages must have a name
      if (message.role === 'function' && !message.name) {
        throw new Error('Function messages must have a name');
      }
    }

    return true;
  }

  /**
   * Validate tools format
   */
  validateTools(tools) {
    if (!Array.isArray(tools)) {
      throw new Error('Tools must be an array');
    }

    for (const tool of tools) {
      if (!tool.type || tool.type !== 'function') {
        throw new Error('Tool type must be "function"');
      }

      if (!tool.function || !tool.function.name) {
        throw new Error('Tool must have function with name');
      }
    }

    return true;
  }

  /**
   * Format error response
   */
  formatError(error, context = {}) {
    return {
      success: false,
      error: error.message,
      provider: this.#name,
      context,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Format success response
   */
  formatSuccess(data, context = {}) {
    return {
      success: true,
      data,
      provider: this.#name,
      context,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Calculate token usage (approximate)
   */
  estimateTokens(text) {
    // Rough estimation: 1 token ≈ 4 characters
    return Math.ceil(text.length / 4);
  }

  /**
   * Prepare messages for API call
   */
  prepareMessages(messages) {
    return messages.map(message => ({
      role: message.role,
      content: message.content,
      ...(message.name && { name: message.name }),
      ...(message.function_call && { function_call: message.function_call })
    }));
  }

  /**
   * Handle rate limiting
   */
  async handleRateLimit(error, retryAfter = null) {
    const delay = retryAfter || 1000;
    this.log('warn', `Rate limited, waiting ${delay}ms`, { error: error.message });
    await new Promise(resolve => setTimeout(resolve, delay));
  }

  /**
   * Get default options
   */
  getDefaultOptions() {
    const config = this.getProviderConfig();
    return {
      temperature: config.temperature || 0.7,
      max_tokens: config.maxTokens || 4096,
      top_p: 1,
      frequency_penalty: 0,
      presence_penalty: 0
    };
  }

  /**
   * Merge options with defaults
   */
  mergeOptions(options = {}) {
    return {
      ...this.getDefaultOptions(),
      ...options
    };
  }

  /**
   * Validate response from API
   */
  validateResponse(response) {
    if (!response) {
      throw new Error('Empty response from API');
    }

    if (!response.choices || !Array.isArray(response.choices)) {
      throw new Error('Invalid response format: missing choices');
    }

    if (response.choices.length === 0) {
      throw new Error('No choices in response');
    }

    return true;
  }

  /**
   * Extract content from response
   */
  extractContent(response) {
    this.validateResponse(response);
    
    const choice = response.choices[0];
    
    if (choice.message) {
      return choice.message.content || '';
    }
    
    if (choice.text) {
      return choice.text;
    }
    
    throw new Error('Unable to extract content from response');
  }

  /**
   * Extract function calls from response
   */
  extractFunctionCalls(response) {
    this.validateResponse(response);
    
    const choice = response.choices[0];
    const message = choice.message;
    
    if (message?.function_call) {
      return [message.function_call];
    }
    
    if (message?.tool_calls) {
      return message.tool_calls.map(call => call.function);
    }
    
    return [];
  }

  /**
   * Get usage statistics from response
   */
  extractUsage(response) {
    return response.usage || {
      prompt_tokens: 0,
      completion_tokens: 0,
      total_tokens: 0
    };
  }
}
