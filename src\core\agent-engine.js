/**
 * Agent Engine - Main orchestrator for AI interactions and tool execution
 */

import { LLMClient } from '../llm/llm-client.js';
import { PromptManager } from '../llm/prompt-manager.js';
import { ToolRegistry } from '../tools/tool-registry.js';
import { RetryLogic } from './retry-logic.js';
import { ErrorHandler } from './error-handler.js';
import { WorkflowManager } from './workflow-manager.js';

export class AgentEngine {
  #config;
  #logger;
  #llmClient;
  #promptManager;
  #toolRegistry;
  #retryLogic;
  #errorHandler;
  #workflowManager;
  #conversationHistory = [];
  #isInitialized = false;

  constructor(config, logger) {
    this.#config = config;
    this.#logger = logger;
  }

  async initialize() {
    if (this.#isInitialized) return;

    this.#logger.info('Initializing Agent Engine');

    try {
      // Initialize core components
      this.#llmClient = new LLMClient(this.#config, this.#logger);
      this.#promptManager = new PromptManager(this.#config, this.#logger);
      this.#toolRegistry = new ToolRegistry(this.#config, this.#logger);
      this.#retryLogic = new RetryLogic(this.#config, this.#logger);
      this.#errorHandler = new ErrorHandler(this.#config, this.#logger);
      this.#workflowManager = new WorkflowManager(this.#config, this.#logger);

      // Initialize LLM client
      await this.#llmClient.initialize();

      this.#isInitialized = true;
      this.#logger.info('Agent Engine initialized successfully');

    } catch (error) {
      this.#logger.error('Failed to initialize Agent Engine', { error: error.message });
      throw error;
    }
  }

  /**
   * Process user input and generate response with tool execution
   */
  async processUserInput(userInput, options = {}) {
    if (!this.#isInitialized) {
      throw new Error('Agent Engine not initialized');
    }

    const startTime = Date.now();
    const sessionId = options.sessionId || Date.now().toString();

    try {
      // Sanitize user input
      const sanitizedInput = this.#promptManager.sanitizeUserInput(userInput);
      
      this.#logger.info('Processing user input', { 
        sessionId,
        inputLength: sanitizedInput.length,
        interactive: options.interactive 
      });

      // Add user message to conversation history
      this.#addToHistory('user', sanitizedInput);

      // Get available tools
      const availableTools = this.#toolRegistry.getToolDefinitions();
      
      // Create system prompt
      const systemPrompt = this.#promptManager.getSystemPrompt(availableTools);
      
      // Format messages for LLM
      const messages = this.#promptManager.formatMessages(
        sanitizedInput,
        systemPrompt,
        this.#conversationHistory.slice(-10) // Last 10 messages for context
      );

      // Generate response with retry logic
      const response = await this.#retryLogic.execute(
        () => this.#generateResponseWithTools(messages, availableTools),
        {
          context: 'LLM response generation',
          maxAttempts: 3
        }
      );

      const duration = Date.now() - startTime;
      
      this.#logger.info('User input processed successfully', {
        sessionId,
        duration,
        hasToolCalls: !!response.toolCalls
      });

      return {
        success: true,
        response: response.content,
        toolCalls: response.toolCalls,
        toolResults: response.toolResults,
        duration,
        sessionId
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.#logger.error('Failed to process user input', {
        sessionId,
        duration,
        error: error.message
      });

      return this.#errorHandler.handleError(error, {
        context: 'user input processing',
        userInput,
        sessionId
      });
    }
  }

  /**
   * Execute a specific workflow
   */
  async executeWorkflow(workflowName, parameters = {}) {
    this.#logger.info(`Executing workflow: ${workflowName}`, { parameters });
    
    try {
      return await this.#workflowManager.executeWorkflow(
        workflowName,
        parameters,
        this.#toolRegistry
      );
    } catch (error) {
      this.#logger.error(`Workflow execution failed: ${workflowName}`, { 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Get agent status and capabilities
   */
  async getStatus() {
    const providerStatus = this.#llmClient.getProviderStatus();
    const availableTools = this.#toolRegistry.getAvailableTools();
    
    return {
      initialized: this.#isInitialized,
      providers: providerStatus,
      tools: availableTools,
      conversationLength: this.#conversationHistory.length,
      configPath: this.#config.getConfigPath(),
      logLevel: this.#config.get('logging.level')
    };
  }

  /**
   * Clear conversation history
   */
  clearHistory() {
    this.#conversationHistory = [];
    this.#logger.info('Conversation history cleared');
  }

  /**
   * Get conversation history
   */
  getHistory() {
    return [...this.#conversationHistory];
  }

  /**
   * Test system connectivity and functionality
   */
  async runDiagnostics() {
    this.#logger.info('Running system diagnostics');
    
    const diagnostics = {
      timestamp: new Date().toISOString(),
      llmProviders: {},
      tools: {},
      overall: 'unknown'
    };

    try {
      // Test LLM providers
      diagnostics.llmProviders = await this.#llmClient.testAllProviders();
      
      // Test tools
      for (const tool of this.#toolRegistry.getAvailableTools()) {
        if (tool.enabled) {
          diagnostics.tools[tool.name] = await this.#testTool(tool.name);
        }
      }

      // Determine overall status
      const hasWorkingProvider = Object.values(diagnostics.llmProviders)
        .some(provider => provider.success);
      const hasWorkingTools = Object.values(diagnostics.tools)
        .some(tool => tool.success);

      diagnostics.overall = hasWorkingProvider && hasWorkingTools ? 'healthy' : 'degraded';

      this.#logger.info('System diagnostics completed', { 
        overall: diagnostics.overall 
      });

      return diagnostics;

    } catch (error) {
      this.#logger.error('Diagnostics failed', { error: error.message });
      diagnostics.overall = 'error';
      diagnostics.error = error.message;
      return diagnostics;
    }
  }

  // Private methods

  async #generateResponseWithTools(messages, availableTools) {
    // First, try to generate a response with tools
    const response = await this.#llmClient.generateCompletionWithTools(
      messages,
      availableTools,
      { temperature: 0.7 }
    );

    if (!response.success) {
      throw new Error(`LLM generation failed: ${response.error}`);
    }

    let result = {
      content: response.data.content || '',
      toolCalls: response.data.toolCalls || [],
      toolResults: []
    };

    // Execute tool calls if any
    if (result.toolCalls && result.toolCalls.length > 0) {
      this.#logger.info(`Executing ${result.toolCalls.length} tool calls`);
      
      result.toolResults = await this.#executeToolCalls(result.toolCalls);
      
      // Generate follow-up response based on tool results
      const followUpMessages = [...messages];
      
      // Add assistant message with tool calls
      followUpMessages.push({
        role: 'assistant',
        content: result.content || 'I need to use some tools to help you.',
        tool_calls: result.toolCalls
      });
      
      // Add tool results
      for (const toolResult of result.toolResults) {
        const content = toolResult.success
          ? JSON.stringify(toolResult.result, null, 2)
          : `Error: ${toolResult.error}`;

        followUpMessages.push({
          role: 'function',
          name: toolResult.tool,
          content: content || 'No output'
        });
      }
      
      // Generate final response
      const finalResponse = await this.#llmClient.generateCompletion(
        followUpMessages,
        { temperature: 0.7 }
      );
      
      if (finalResponse.success) {
        result.content = finalResponse.data.content;
      }
    }

    // Add assistant response to history
    this.#addToHistory('assistant', result.content);

    return result;
  }

  async #executeToolCalls(toolCalls) {
    const results = [];
    
    for (const toolCall of toolCalls) {
      try {
        this.#logger.debug(`Executing tool: ${toolCall.function.name}`, {
          arguments: toolCall.function.arguments
        });
        
        const result = await this.#retryLogic.execute(
          () => this.#toolRegistry.executeTool(
            toolCall.function.name,
            toolCall.function.arguments
          ),
          {
            context: `tool execution: ${toolCall.function.name}`,
            maxAttempts: 2
          }
        );
        
        results.push({
          id: toolCall.id,
          tool: toolCall.function.name,
          success: true,
          result
        });
        
      } catch (error) {
        this.#logger.error(`Tool execution failed: ${toolCall.function.name}`, {
          error: error.message
        });
        
        results.push({
          id: toolCall.id,
          tool: toolCall.function.name,
          success: false,
          error: error.message
        });
      }
    }
    
    return results;
  }

  async #testTool(toolName) {
    try {
      const tool = this.#toolRegistry.getTool(toolName);
      
      if (!tool) {
        return { success: false, error: 'Tool not found' };
      }

      // Simple test based on tool type
      let testParams;
      switch (toolName) {
        case 'shell':
          testParams = { command: 'echo "test"' };
          break;
        case 'web':
          testParams = { url: 'https://httpbin.org/status/200', timeout: 5000 };
          break;
        default:
          return { success: false, error: 'No test available for this tool' };
      }

      const result = await tool.execute(testParams);
      return { success: result.success };

    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  #addToHistory(role, content) {
    this.#conversationHistory.push({
      role,
      content,
      timestamp: new Date().toISOString()
    });

    // Keep only last 50 messages to prevent memory issues
    if (this.#conversationHistory.length > 50) {
      this.#conversationHistory = this.#conversationHistory.slice(-50);
    }
  }
}
